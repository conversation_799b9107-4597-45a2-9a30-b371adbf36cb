#!/usr/bin/env python3
"""
Test script to verify the new authentication format works correctly.
"""

import asyncio
import json
import os
import tempfile
from backend.auth import do_register, do_login, _load_users, _save_users


async def test_auth_format():
    """Test the new authentication format."""
    print("🧪 Testing new authentication format...")
    
    # Create a temporary test database
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        test_db_path = f.name
        json.dump({}, f)
    
    try:
        # Temporarily override the USER_DB_PATH
        original_path = os.environ.get('USER_DB_PATH', '')
        os.environ['USER_DB_PATH'] = test_db_path
        
        # Test registration
        print("📝 Testing registration...")
        result = await do_register("testuser", "TestPass123!", "<EMAIL>")
        print(f"Registration result: {result}")
        
        # Check the database format
        users = _load_users(test_db_path)
        print(f"Database content: {json.dumps(users, indent=2)}")
        
        # Verify the format is correct
        if "testuser" in users:
            user_data = users["testuser"]
            expected_keys = {"password", "email", "created_at"}
            actual_keys = set(user_data.keys())
            
            if expected_keys.issubset(actual_keys):
                print("✅ User format is correct!")
                print(f"   User data keys: {list(actual_keys)}")
            else:
                print(f"❌ User format is incorrect!")
                print(f"   Expected keys: {expected_keys}")
                print(f"   Actual keys: {actual_keys}")
        else:
            print("❌ User not found in database!")
        
        # Test login
        print("🔐 Testing login...")
        login_result = await do_login("testuser", "TestPass123!")
        print(f"Login result: {login_result}")
        
        # Test login with email
        print("📧 Testing login with email...")
        email_login_result = await do_login("<EMAIL>", "TestPass123!")
        print(f"Email login result: {email_login_result}")
        
        # Test duplicate username
        print("🔄 Testing duplicate username...")
        duplicate_result = await do_register("testuser", "AnotherPass123!", "<EMAIL>")
        print(f"Duplicate registration result: {duplicate_result}")
        
        print("✅ Authentication format test completed!")
        
    finally:
        # Restore original path and cleanup
        if original_path:
            os.environ['USER_DB_PATH'] = original_path
        else:
            os.environ.pop('USER_DB_PATH', None)
        
        # Clean up test file
        try:
            os.unlink(test_db_path)
        except:
            pass


if __name__ == "__main__":
    asyncio.run(test_auth_format())
