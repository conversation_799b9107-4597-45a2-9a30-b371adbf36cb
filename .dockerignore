# Git and version control
.git
.gitignore
.gitattributes

# Python cache and virtual environments
__pycache__
*.pyc
*.pyo
*.pyd
*.tar
.Python
env/
venv/
.venv/
ENV/
env.bak/
venv.bak/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs and temporary files
logs/
*.log
*.tmp
*.temp

# Documentation
README.md
*.md
docs/

# Docker files (except the main Dockerfile)
Dockerfile
Dockerfile.dev
Dockerfile.test
docker-compose*.yml

# Development and build files
build/
dist/
*.egg-info/

# Data directories (will be created at runtime)

# Misc
.ruff_cache/
.mypy_cache/

# Test files and directories (excluded for production and development builds)
.pytest_cache/
.coverage
htmlcov/

# Note: setup.py is included for all builds
# Test files are excluded by default but can be explicitly copied in test builds

__pycache__/
.ruff_cache/
.pre-commit-config.yaml
.markdownlint.yaml
.env.dev
