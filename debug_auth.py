#!/usr/bin/env python3
"""Debug script to test authentication functions directly."""

import asyncio
import json


async def test_auth():
    """Test authentication functions directly."""
    print("🔍 Testing authentication functions directly...")
    print("=" * 60)

    # Import here to avoid issues
    from backend.auth import do_register, do_login

    # Test registration first
    print("\n1. Testing Registration:")
    print("-" * 30)

    test_username = "testuser123"
    test_email = "<EMAIL>"
    test_password = "testpass123"

    print(f"Registering user: {test_username}")
    print(f"Email: {test_email}")
    print(f"Password: {test_password}")

    register_result = await do_register(test_username, test_password, test_email)
    print(f"\nRegistration result:")
    print(f"Type: {type(register_result)}")
    print(f"Content: {json.dumps(register_result, indent=2)}")

    # Test login
    print("\n\n2. Testing Login:")
    print("-" * 30)

    print(f"Logging in user: {test_username}")
    print(f"Password: {test_password}")

    login_result = await do_login(test_username, test_password)
    print(f"\nLogin result:")
    print(f"Type: {type(login_result)}")
    print(f"Content: {json.dumps(login_result, indent=2)}")

    # Test the specific condition that the frontend checks
    print(f"\nFrontend condition check:")
    print(f"login_result.get('status'): '{login_result.get('status')}'")
    print(f"login_result.get('status') == 'success': {login_result.get('status') == 'success'}")
    print(f"login_result.get('message'): '{login_result.get('message')}'")

    print("\n" + "=" * 60)
    print("🎯 Authentication test completed!")


if __name__ == "__main__":
    asyncio.run(test_auth())
