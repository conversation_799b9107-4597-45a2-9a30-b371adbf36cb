"""
Infrastructure utilities for the NYP FYP Chatbot project.

This package contains utility modules for:
- NLTK configuration and data management
- Other infrastructure-related functionality
"""

# Import functions from the main infra_utils.py file to maintain compatibility
import sys
import os
from pathlib import Path

# Add the parent directory to the path to import from infra_utils.py
parent_dir = Path(__file__).parent.parent
sys.path.insert(0, str(parent_dir))

try:
    from infra_utils import (
        rel2abspath,
        create_folders,
        ensure_chatbot_dir_exists,
        get_chatbot_dir,
        cleanup_test_environment,
        get_docker_venv_path,
        get_docker_venv_python
    )

    # Make these available at package level
    __all__ = [
        'rel2abspath',
        'create_folders',
        'ensure_chatbot_dir_exists',
        'get_chatbot_dir',
        'cleanup_test_environment',
        'get_docker_venv_path',
        'get_docker_venv_python'
    ]

except ImportError as e:
    # If we can't import from the main file, define minimal stubs
    def get_docker_venv_path(mode="prod"):
        return f"/home/<USER>/.nypai-chatbot/venv-{mode}"

    def get_docker_venv_python(mode="prod"):
        return f"/home/<USER>/.nypai-chatbot/venv-{mode}/bin/python"
